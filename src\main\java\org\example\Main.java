package org.example;


import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import org.apache.pulsar.client.api.*;
import org.apache.pulsar.shade.com.fasterxml.jackson.databind.ObjectMapper;
import java.util.function.BiConsumer;


import java.sql.*;
import java.text.SimpleDateFormat;


import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;


public class Main {
    private static final String BROKER_SERVICE_URL = "pulsar://pulsar.scc.com:6650";
    private static final String MEDICINE_TOPIC = "persistent://spc/301-interface/labMedicineData";
    private static final String TOKEN =
            "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzcGMtMzAxIn0.MQEs2IYyRUB1zzSihBfmgUtcF9gp5CE308I8JEjscpM";
    public static Thread taskThread;
    private static boolean isRunning = false;
    static boolean isPaused = false;

    // 添加公共方法获取运行状态
    public static boolean isRunning() {
        return isRunning;
    }
    private static PulsarClient pulsarClient;
    private static org.apache.pulsar.client.api.Consumer<byte[]> consumer;
    private static final HikariDataSource dataSource;
    private static final HikariDataSource dataSourceCloud;
    // 新增变量来累积所有数据

    static {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("****************************************************************");
        config.setUsername("sa");
        config.setPassword("root1234");
        config.setMaximumPoolSize(10); // 优化：减少最大连接数以节省内存
        config.setMinimumIdle(2); // 优化：减少最小空闲连接数
        config.setIdleTimeout(60000); // 优化：增加空闲超时时间
        config.setMaxLifetime(1800000); // 优化：增加连接最大生命周期
        config.setConnectionTimeout(30000); // 添加连接超时配置
        config.setLeakDetectionThreshold(60000); // 添加连接泄漏检测
        dataSource = new HikariDataSource(config);
    }

    static {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("jdbc:oracle:thin:@//************:1521/SPCSZSUB1YS");
        config.setUsername("spc");
        config.setPassword("scc123456");
        config.setMaximumPoolSize(8); // 优化：减少最大连接数以节省内存
        config.setMinimumIdle(2); // 优化：减少最小空闲连接数
        config.setIdleTimeout(60000); // 优化：增加空闲超时时间
        config.setMaxLifetime(1800000); // 优化：增加连接最大生命周期
        config.setConnectionTimeout(30000); // 添加连接超时配置
        config.setLeakDetectionThreshold(60000); // 添加连接泄漏检测
        config.setPoolName("CloudDataSourcePool"); // 设置数据源的名称
        config.setRegisterMbeans(true); // 启用JMX监控
        dataSourceCloud = new HikariDataSource(config);
    }

    /**
     * 主方法，程序的入口
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 定期检查新数据
        runMainLogic();
    }



    // 在需要记录日志的地方使用log方法
    private static void processMessage(Connection connection, ObjectMapper objectMapper, Message<byte[]> msg) {
        try {
            Chemical chemical = objectMapper.readValue(msg.getData(), Chemical.class);
            insertDataIntoDatabase(connection, chemical);
            log("成功处理并插入数据 ID：" + chemical.getId());
        } catch (Exception e) {
            logError("处理消息时出错：" + e.getMessage());
            e.printStackTrace();
        }
    }

    // 修改 insertDataIntoDatabase 方法以插入数据
    private static void insertDataIntoDatabase(Connection connection, Chemical entity) {
        // 创建 SQL 插入语句，根据表列名进行调整
        String sql = "INSERT INTO dbo.chemical (id, organization_id, attribute_id, examine_date, shift, staff, " +
                "department_code, process_set_name, process_name, product_set_name, product_name, test_set_name, " +
                "test_name, sample_size, layer_number, upper_limit, median_specification, down_limit, examine1, " +
                "examine2, created_by, last_updated_by, creation_date, last_update_date, status, frequency," +
                " " +
                "frequency_unit, slot_body_name, project_team_code, project_team_name, test_code, " +
                "adjustment_upper_limit, " +
                "adjustment_mid, adjustment_lower_limit, project_unit,insertion_time,is_exported,warning_upper_limit," +
                "warning_mid,warning_lower_limit,attribute1,attribute2,attribute3) VALUES (?, ?, ?, ?, ?, ?," +
                " ?, ?, ?, ?," +
                " ?, ?, ?, ?, " +
                "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?,?,?)";
        try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {

            preparedStatement.setString(1, entity.getId());
            setNullableLong(preparedStatement, 2, entity.getOrganizationId());
            setNullableLong(preparedStatement, 3, entity.getAttributeId());
            preparedStatement.setTimestamp(4, entity.getExamineDate());
            preparedStatement.setString(5, entity.getShift());
            preparedStatement.setString(6, entity.getStaff());
            preparedStatement.setString(7, entity.getDepartmentCode());
            preparedStatement.setString(8, entity.getProcessSetName());
            preparedStatement.setString(9, entity.getProcessName());
            preparedStatement.setString(10, entity.getProductSetName());
            preparedStatement.setString(11, entity.getProductName());
            preparedStatement.setString(12, entity.getTestSetName());
            preparedStatement.setString(13, entity.getTestName().trim());
            preparedStatement.setString(14, entity.getSampleSize());
            preparedStatement.setString(15, entity.getLayerNumber());
            preparedStatement.setString(16, entity.getUpperLimit());
            preparedStatement.setString(17, entity.getMedianSpecification());
            preparedStatement.setString(18, entity.getDownLimit());
            preparedStatement.setString(19, entity.getExamine1());
            preparedStatement.setString(20, entity.getExamine2());
            preparedStatement.setString(21, entity.getCreatedBy());
            preparedStatement.setString(22, entity.getLastUpdatedBy());
            preparedStatement.setTimestamp(23, entity.getCreationDate());
            preparedStatement.setTimestamp(24, entity.getLastUpdateDate());
            setNullableLong(preparedStatement, 25, entity.getStatus());
            preparedStatement.setString(26, entity.getFrequency());
            preparedStatement.setString(27, entity.getFrequencyUnit());
            preparedStatement.setString(28, entity.getSlotBodyName());
            preparedStatement.setString(29, entity.getProjectTeamCode());
            preparedStatement.setString(30, entity.getProjectTeamName());
            preparedStatement.setString(31, entity.getTestCode());
            preparedStatement.setString(32, entity.getAdjustmentUpperLimit());
            preparedStatement.setString(33, entity.getAdjustmentMid());
            preparedStatement.setString(34, entity.getAdjustmentLowerLimit());
            preparedStatement.setString(35, entity.getProjectUnit());
            preparedStatement.setTimestamp(36, new Timestamp(System.currentTimeMillis()));
            preparedStatement.setBoolean(37, false);
            preparedStatement.setString(38, entity.getWarningUpperLimit());
            preparedStatement.setString(39, entity.getWarningMid());
            preparedStatement.setString(40, entity.getWarningLowerLimit());
            preparedStatement.setString(41, entity.getAttribute1());
            preparedStatement.setString(42, entity.getAttribute2());
            preparedStatement.setString(43, entity.getAttribute3());
            int rowsInserted = preparedStatement.executeUpdate();
            if (rowsInserted > 0) {
                log("数据成功插入 ID：" + entity.getId()+" "+entity.getTestName());
            }
        } catch (SQLException e) {
            logError("插入数据时出错：" + e.getMessage());
            handleSQLException(e);
        }
    }

    private static void setNullableLong(PreparedStatement preparedStatement, int parameterIndex, Long value) throws SQLException {
        if (value != null) {
            preparedStatement.setLong(parameterIndex, value);
        } else {
            preparedStatement.setNull(parameterIndex, Types.BIGINT);
        }
    }

    private static void handleSQLException(SQLException e) {
        if (e.getErrorCode() == 2627) {
            log("数据插入失败，该数据已存在。");
        } else {
            e.printStackTrace();
        }
    }

    private static String getF_PRCSValueForDepartment(Connection remoteConnection, String departmentName) {
        String sql = "SELECT F_PRCS FROM SPC.PRCS_DAT WHERE F_NAME = ?";
        try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
            preparedStatement.setString(1, departmentName);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getString("F_PRCS");
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null; // 如果没有找到对应的 F_PRCS 值
    }

    private static String getF_TESTValueForTestName(Connection remoteConnection, String testName) {
        String sql = "SELECT F_TEST FROM SPC.TEST_DAT WHERE F_NAME = ?";
        try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
            preparedStatement.setString(1, testName);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getString("F_TEST");
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null; // 如果没有找到对应的 F_TEST 值
    }

    private static String getF_PARTValueForProductName(Connection remoteConnection, String productName) {
        String sql = "SELECT F_PART FROM SPC.PART_DAT WHERE F_NAME = ?";
        try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
            preparedStatement.setString(1, productName);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getString("F_PART");
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null; // 如果没有找到对应的 F_PART 值
    }

    private static CalculatedValues calculateControlValues(ControlLimits controlLimits, String testName) {
        // 定义测试名与样本量的映射
        Map<String, Integer> sampleSizeMap = new HashMap<String, Integer>();
        sampleSizeMap.put("PD全线微蚀量", 3);
        sampleSizeMap.put("TR微蚀微蚀量", 5);
        sampleSizeMap.put("TR微蚀微蚀量(2.1线速)", 5);
        sampleSizeMap.put("TROSPOSP膜厚", 3);
        sampleSizeMap.put("TROSPOSP膜厚(2.1线速)", 3);
        double intermediateValue = 6 * controlLimits.fSp;
        // 如果测试名在映射中，根据样本量调整intermediateValue的计算
        if (sampleSizeMap.containsKey(testName)) {
            int sampleSize = sampleSizeMap.get(testName);
            intermediateValue = intermediateValue / Math.sqrt(sampleSize);
        }
        double upperControlLimit = controlLimits.fMean + intermediateValue / 2;
        double lowerControlLimit = controlLimits.fMean - intermediateValue / 2;
        double lcl1 = 0.833* lowerControlLimit + 0.167* upperControlLimit;
        double ucl1 = 0.167* lowerControlLimit + 0.833* upperControlLimit;
        return new CalculatedValues(upperControlLimit, lowerControlLimit, lcl1, ucl1);
    }

    private static double calculateExamineValue(double examine1, CalculatedValues calculatedValues) {
        if (examine1 < calculatedValues.lcl1) {
            return calculatedValues.lcl1 + (calculatedValues.lcl1 - examine1) % (calculatedValues.ucl1 - calculatedValues.lcl1);
        } else if (examine1 > calculatedValues.ucl1) {
            return calculatedValues.ucl1 - (examine1 - calculatedValues.ucl1) % (calculatedValues.ucl1 - calculatedValues.lcl1);
        }
        return examine1;
    }

    public static void processChemicalData(Connection localConnection, Connection remoteConnection) throws SQLException {
        // 优化：限制查询结果数量，避免一次性加载过多数据
        String localSql = "SELECT TOP 1000 dc.*, dept.department_name FROM dbo.chemical dc " +
                "JOIN department dept ON dc.department_code = dept.department_code " +
                "WHERE COALESCE(dc.is_exported, 0) = 0 AND COALESCE(dc.not_process, 0) = 0 " +
                "ORDER BY dc.insertion_time ASC";

        // 优化：使用批处理更新
        String exportedUpdateSql = "UPDATE dbo.chemical SET is_exported = 1 WHERE id = ?";
        String notProcessUpdateSql = "UPDATE dbo.chemical SET not_process = 1 WHERE id = ?";

        try (PreparedStatement localPreparedStatement = localConnection.prepareStatement(localSql);
             PreparedStatement exportedStmt = localConnection.prepareStatement(exportedUpdateSql);
             PreparedStatement notProcessStmt = localConnection.prepareStatement(notProcessUpdateSql)) {

            try (ResultSet localResultSet = localPreparedStatement.executeQuery()) {
                int batchCount = 0;
                while (localResultSet.next() && isRunning) {
                    if (localResultSet.getString("upper_limit")==null||localResultSet.getString("upper_limit").equals("null")) {
                        continue;
                    }
                    if (localResultSet.getDate("examine_date") == null) {
                        continue; // Skip this record if examine_date is null
                    }
                    String id = localResultSet.getString("id");
                    boolean isExported = processDataRecord(localResultSet, remoteConnection, localConnection);

                    if (isExported) {
                        exportedStmt.setString(1, id);
                        exportedStmt.addBatch();
                    } else {
                        notProcessStmt.setString(1, id);
                        notProcessStmt.addBatch();
                    }

                    batchCount++;
                    // 每100条记录执行一次批处理
                    if (batchCount % 100 == 0) {
                        exportedStmt.executeBatch();
                        notProcessStmt.executeBatch();
                    }
                }
                // 执行剩余的批处理
                exportedStmt.executeBatch();
                notProcessStmt.executeBatch();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    // 新增方法：处理单条数据记录
    private static boolean processDataRecord(ResultSet resultSet, Connection remoteConnection, Connection localConnection) throws SQLException {
        String result = extractDataLine(resultSet, remoteConnection, localConnection);
        if (result != null) {
            String examine1 = resultSet.getString("examine1");
            Chemical entity = resultToChemical(result, examine1);
            if (entity != null) {
                insertDataIntoChemicalYsTable(localConnection, entity);
                return true;
            }
        }
        return false;
    }





    private static String extractDataLine(ResultSet localResultSet, Connection remoteConnection,Connection localConnection) throws SQLException {
        // 从 resultSet 提取数据并格式化为一行 CSV 字符串
        String id = localResultSet.getString("id");
        String organizationId = localResultSet.getString("organization_id");
        String attributeId = localResultSet.getString("attribute_id");
        String examineDate = localResultSet.getString("examine_date");
        String shift = localResultSet.getString("shift");
        String staff = localResultSet.getString("staff");
        String processSetName = localResultSet.getString("department_code");
        String processName = localResultSet.getString("department_name");
        String productSetName = localResultSet.getString("product_set_name");
        String productName = localResultSet.getString("process_name");
        String testSetName = localResultSet.getString("test_set_name");
        String testName = localResultSet.getString("test_name");
        String sampleSize = localResultSet.getString("sample_size");
        String layerNumber = localResultSet.getString("layer_number");
        String upperLimit = localResultSet.getString("upper_limit");
        String medianSpecification = localResultSet.getString("median_specification");
        String downLimit = localResultSet.getString("down_limit");
        double examine2 = localResultSet.getDouble("examine2");
        String createdBy = localResultSet.getString("created_by");
        String lastUpdatedBy = localResultSet.getString("last_updated_by");
        String creationDate = localResultSet.getString("creation_date");
        String lastUpdateDate = localResultSet.getString("last_update_date");
        String status = localResultSet.getString("status");
        String frequency = localResultSet.getString("frequency");
        String frequencyUnit = localResultSet.getString("frequency_unit");
        String slotBodyName = localResultSet.getString("slot_body_name");
        String projectTeamCode = localResultSet.getString("project_team_code");
        String projectTeamName = localResultSet.getString("project_team_name");
        String testCode = localResultSet.getString("test_code");
        String adjustmentUpperLimit = localResultSet.getString("adjustment_upper_limit");
        String adjustmentMid = localResultSet.getString("adjustment_mid");
        String adjustmentLowerLimit = localResultSet.getString("adjustment_lower_limit");
        String projectUnit = localResultSet.getString("project_unit");
        String departmentName = localResultSet.getString("department_name");
        double examine1 = localResultSet.getDouble("examine1");
        String warningUpperLimit = localResultSet.getString("warning_upper_limit");
        String warningMid = localResultSet.getString("warning_mid");
        String warningLowerLimit = localResultSet.getString("warning_lower_limit");

        // 检查是否存在于rule表中
        int onlyUpperLimit = getOnlyUpperLimitFlag(localConnection, productName, processName, testName);
        if (onlyUpperLimit == 1||onlyUpperLimit == 2) {
            StringBuilder csvLine = new StringBuilder();
            csvLine.append(id).append(",").append(organizationId).append(",").append(attributeId).append(",")
                    .append(examineDate).append(",").append(shift).append(",").append(staff).append(",")
                    .append(processSetName).append(",").append(processName).append(",").append(productSetName).append(",")
                    .append(productName).append(",").append(testSetName).append(",").append(testName).append(",")
                    .append(sampleSize).append(",").append(layerNumber).append(",").append(upperLimit).append(",")
                    .append(medianSpecification).append(",").append(downLimit).append(",").append(examine1)
                    .append(",").append(examine2).append(",").append(createdBy).append(",").append(lastUpdatedBy).append(",")
                    .append(creationDate).append(",").append(lastUpdateDate).append(",").append(status).append(",")
                    .append(frequency).append(",").append(frequencyUnit).append(",").append(slotBodyName).append(",")
                    .append(projectTeamCode).append(",").append(projectTeamName).append(",").append(testCode).append(",")
                    .append(adjustmentUpperLimit).append(",").append(adjustmentMid).append(",").append(adjustmentLowerLimit)
                    .append(",").append(projectUnit).append(",").append(warningUpperLimit).append(",").append(warningMid).append(",").append(warningLowerLimit);
            return csvLine.toString();
        }
        // 获取远程数据库中的 F_PRCS, F_TEST, F_PART 值
        String fTestValue = getF_TESTValueForTestName(remoteConnection, testName);
        if (fTestValue == null) {
            return null;
        }
        String fPrcsValue = getF_PRCSValueForDepartment(remoteConnection, departmentName);
        if (fPrcsValue == null) {
            return null;
        }
        String fPartValue = getF_PARTValueForProductName(remoteConnection, productName);
        if (fPartValue == null) {
            return null;
        }

        // 获取控制限制
        ControlLimits controlLimits = ControlLimitsCache.getControlLimits(remoteConnection, fPrcsValue, fTestValue,
                fPartValue);

            if (controlLimits != null) {
                Double adjustedExamine1;
                // 在计算控制值之前
                if ("LV蓬松膨胀剂E".equals(testName)) {
                    adjustedExamine1 = handleSpecialCaseForLV(String.valueOf(examine2), examine1);
                } else {
                    // 计算控制值
                    CalculatedValues calculatedValues = calculateControlValues(controlLimits, testName);
                    adjustedExamine1 = calculateExamineValue(examine1, calculatedValues);
                }
                double adjustedExamine2 = examine2 != 0 ? Math.round(adjustedExamine1 * examine2 / examine1 * 1000.0) / 1000.0 : 0; // 使用3位小数精度
                StringBuilder csvLine = new StringBuilder();
                if (!isPaused) {
                    csvLine.append(id).append(",").append(organizationId).append(",").append(attributeId).append(",")
                            .append(examineDate).append(",").append(shift).append(",").append(staff).append(",")
                            .append(processSetName).append(",").append(processName).append(",").append(productSetName).append(",")
                            .append(productName).append(",").append(testSetName).append(",").append(testName).append(",")
                            .append(sampleSize).append(",").append(layerNumber).append(",").append(upperLimit).append(",")
                            .append(medianSpecification).append(",").append(downLimit).append(",").append(roundExamineValue(adjustedExamine1))
                            .append(",").append(adjustedExamine2).append(",").append(createdBy).append(",").append(lastUpdatedBy).append(",")
                            .append(creationDate).append(",").append(lastUpdateDate).append(",").append(status).append(",")
                            .append(frequency).append(",").append(frequencyUnit).append(",").append(slotBodyName).append(",")
                            .append(projectTeamCode).append(",").append(projectTeamName).append(",").append(testCode).append(",")
                            .append(adjustmentUpperLimit).append(",").append(adjustmentMid).append(",").append(adjustmentLowerLimit)
                            .append(",").append(projectUnit).append(",").append(warningUpperLimit).append(",").append(warningMid).append(",").append(warningLowerLimit);
                    return csvLine.toString();
                } else {
                    csvLine.append(id).append(",").append(organizationId).append(",").append(attributeId).append(",")
                            .append(examineDate).append(",").append(shift).append(",").append(staff).append(",")
                            .append(processSetName).append(",").append(processName).append(",").append(productSetName).append(",")
                            .append(productName).append(",").append(testSetName).append(",").append(testName).append(",")
                            .append(sampleSize).append(",").append(layerNumber).append(",").append(upperLimit).append(",")
                            .append(medianSpecification).append(",").append(downLimit).append(",").append(examine1)
                            .append(",").append(examine2).append(",").append(createdBy).append(",").append(lastUpdatedBy).append(",")
                            .append(creationDate).append(",").append(lastUpdateDate).append(",").append(status).append(",")
                            .append(frequency).append(",").append(frequencyUnit).append(",").append(slotBodyName).append(",")
                            .append(projectTeamCode).append(",").append(projectTeamName).append(",").append(testCode).append(",")
                            .append(adjustmentUpperLimit).append(",").append(adjustmentMid).append(",").append(adjustmentLowerLimit)
                            .append(",").append(projectUnit).append(",").append(warningUpperLimit).append(",").append(warningMid).append(",").append(warningLowerLimit);
                    return csvLine.toString();
                }
            }

        return null;
    }

    private static int getOnlyUpperLimitFlag(Connection localConnection, String productName, String processName, String testName) throws SQLException {
        String sql = "SELECT only_upper_limit FROM dbo.[rule] WHERE product_name = ? AND process_name = ? AND test_name = ?";
        try (PreparedStatement stmt = localConnection.prepareStatement(sql)) {
            stmt.setString(1, productName);
            stmt.setString(2, processName);
            stmt.setString(3, testName);

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt("only_upper_limit");
            }
        }
        return 0; // 默认返回0表示没有找到对应的规则，或者only_upper_limit不为1
    }

    private static double handleSpecialCaseForLV(String examine2Str, double examine1) {
        double examine2 = Double.parseDouble(examine2Str);
        double[] examine2Options = {14.0, 14.5, 15.0, 16.0};
        double[] examine1Mappings = {166.60, 172.55, 178.50, 190.40};
        double closest = examine2Options[0];
        double diff = Math.abs(examine2 - closest);

        for (int i = 1; i < examine2Options.length; i++) {
            double newDiff = Math.abs(examine2 - examine2Options[i]);
            if (newDiff < diff) {
                closest = examine2Options[i];
                diff = newDiff;
            }
        }

        for (int i = 0; i < examine2Options.length; i++) {
            if (closest == examine2Options[i]) {
                return examine1Mappings[i];
            }
        }

        return examine1; // 默认情况，返回原始 examine1
    }

    private static double roundExamineValue(double value) {
        return Math.round(value * 1000.0) / 1000.0;
    }

    public static synchronized void startTask() throws PulsarClientException {
        if (isRunning) {
            stopTask(); // 先停止当前任务
        }

        isRunning = true;
        isPaused = false;

        taskThread = new Thread(Main::runMainLogic);
        taskThread.setDaemon(true); // 设置为守护线程
        taskThread.start();
    }

    public static synchronized void pauseTask() {
        isPaused = true;
        log("任务已暂停。");
    }

    public static synchronized void resumeTask() {
        isPaused = false;
        log("任务已恢复。");
    }

    public static synchronized void stopTask() throws PulsarClientException {
        isRunning = false;
        if (taskThread != null && taskThread.isAlive()) {
            taskThread.interrupt();
            try {
                taskThread.join(5000); // 等待任务线程停止
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        shutdown();
        log("任务已停止。");
    }

    private static void initializePulsarClient() {
        try {
            pulsarClient = PulsarClient.builder()
                    .serviceUrl(BROKER_SERVICE_URL)
                    .authentication(AuthenticationFactory.token(TOKEN))
                    .build();
        } catch (PulsarClientException e) {
            e.printStackTrace();
        }
    }

    private static void initializeConsumer() {
        try {
            consumer = pulsarClient.newConsumer(Schema.BYTES)
                    .subscriptionName("SZ301-Chemical")
                    .subscriptionType(SubscriptionType.Exclusive)
                    .subscriptionInitialPosition(SubscriptionInitialPosition.Earliest)
                    .topic(MEDICINE_TOPIC)
                    .subscribe();
        } catch (PulsarClientException e) {
            logError("初始化消费者失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void runMainLogic() {
        initializePulsarClient();
        initializeConsumer();

        // 定时任务执行器 - 优化：减少线程数以节省内存
        ScheduledExecutorService executorService = Executors.newScheduledThreadPool(1);

        // 定时任务：数据读取任务
        Runnable dataReadingTask = () -> {
            log("数据读取任务开始");
            int messageCount = 0;
            int maxMessages = 100; // 限制每次处理的消息数量

            try (Connection connection = getDatabaseConnection()) {
                while (isRunning && messageCount < maxMessages) {
                    try {
                        Message<byte[]> msg = consumer.receive(3, TimeUnit.SECONDS); // 优化：减少超时时间
                        if (msg != null) {
                            log("接收到新数据，开始处理");
                            processMessage(connection, new ObjectMapper(), msg);
                            consumer.acknowledge(msg);
                            messageCount++;
                        } else {
                            log("当前无新数据，等待下次检查");
                            break; // 如果没有消息，结束当前循环
                        }
                    } catch (Exception e) {
                        logError("处理消息时出错：" + e.getMessage());
                        // 继续处理下一条消息，不中断整个任务
                    }
                }
            } catch (Exception e) {
                logError("数据读取任务出错：" + e.getMessage());
                e.printStackTrace();
            }
            log("数据读取任务完成，处理了 " + messageCount + " 条消息");
        };

        // 定时任务：日志读取任务
        Runnable logReadingTask = () -> {
            log("日志读取任务开始");
            int processedCount = 0;

            try (Connection localConnection = dataSource.getConnection();
                 Connection remoteConnection = dataSourceCloud.getConnection()) {
                printConnectionPoolStats();
                // 优化：限制日志查询数量，按时间排序
                String logSql = "SELECT TOP 200 * FROM dbo.chemical_log WHERE processed = 0 ORDER BY log_id ASC";
                try (PreparedStatement logStmt = localConnection.prepareStatement(logSql);
                     ResultSet logRs = logStmt.executeQuery()) {

                    while (logRs.next() && isRunning) {
                        try {
                            String chemicalId = logRs.getString("chemical_id");
                            boolean isExported = false;
                            // 优化：只查询需要的字段
                            String chemicalSql = "SELECT dc.*, dept.department_name FROM dbo.chemical dc " +
                                    "JOIN department dept ON dc.department_code = dept.department_code WHERE dc.id = ?";
                            try (PreparedStatement chemicalStmt = localConnection.prepareStatement(chemicalSql)) {
                                chemicalStmt.setString(1, chemicalId);
                                try (ResultSet chemicalRs = chemicalStmt.executeQuery()) {
                                    if (chemicalRs.next()) {
                                        if (chemicalRs.getString("upper_limit")==null||chemicalRs.getString("upper_limit").equals("null")) {
                                            continue;
                                        }
                                        if (chemicalRs.getDate("examine_date") == null) {
                                            continue; // Skip this record if examine_date is null
                                        }
                                        String id = chemicalRs.getString("id");
                                        isExported = processDataRecord(chemicalRs, remoteConnection, localConnection);
                                        if (isExported) {
                                            // 直接更新数据库标记为已导出
                                            String updateSql = "UPDATE dbo.chemical SET is_exported = 1 WHERE id = ?";
                                            try (PreparedStatement updateStmt = localConnection.prepareStatement(updateSql)) {
                                                updateStmt.setString(1, id);
                                                updateStmt.executeUpdate();
                                            }
                                        }
                                    }
                                }
                            }
                            // 更新 log_table 表，将数据标记为已处理
                            String updateLogSql = "UPDATE dbo.chemical_log SET processed = 1 WHERE log_id = ?";
                            try (PreparedStatement updateLogStmt = localConnection.prepareStatement(updateLogSql)) {
                                updateLogStmt.setInt(1, logRs.getInt("log_id"));
                                updateLogStmt.executeUpdate();
                            }
                            processedCount++;
                        } catch (Exception e) {
                            logError("处理日志记录时出错：" + e.getMessage());
                            // 继续处理下一条记录
                        }
                    }
                }
            } catch (SQLException e) {
                logError("日志读取任务出错：" + e.getMessage());
                e.printStackTrace();
            }
            log("日志读取任务完成，处理了 " + processedCount + " 条记录");
        };

        // 定时任务：数据处理任务
        Runnable dataProcessingTask = () -> {
            log("数据处理任务开始");
            try (Connection localConnection = getDatabaseConnection();
                 Connection remoteConnection = dataSourceCloud.getConnection()) {
                processChemicalData(localConnection, remoteConnection);
            } catch (SQLException e) {
                e.printStackTrace();
            }
            log("数据处理任务完成");
        };

        // 优化：计划任务顺序执行，添加更好的错误处理
        executorService.scheduleWithFixedDelay(() -> {
            if (Thread.currentThread().isInterrupted() || !isRunning) {
                log("任务线程被中断或已停止，停止执行。");
                return;
            }
            try {
                if (!isPaused) {
                    log("开始执行定时任务循环");
                    dataReadingTask.run();

                    // 检查是否需要继续
                    if (Thread.currentThread().isInterrupted() || !isRunning) return;
                    logReadingTask.run();

                    // 检查是否需要继续
                    if (Thread.currentThread().isInterrupted() || !isRunning) return;
                    dataProcessingTask.run();

                    log("定时任务循环完成");
                } else {
                    log("任务已暂停，跳过本次执行");
                }
            } catch (Exception e) {
                logError("定时任务执行出错：" + e.getMessage());
                e.printStackTrace();
            }
        }, 0, 6, TimeUnit.HOURS); // 优化：减少执行频率到6小时

        // 添加JVM关闭钩子以确保资源正确释放
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log("正在关闭，释放资源...");
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
            }
            try {
                shutdown();
            } catch (PulsarClientException e) {
                e.printStackTrace();
            }
        }));
    }

    private static BiConsumer<String,Boolean> logConsumer;

    public static void setLogConsumer(BiConsumer<String,Boolean> consumer) {
        logConsumer = consumer;
    }

    private static void log(String message) {
        if (logConsumer != null) {
            logConsumer.accept(message,false);
        }
        System.out.println(message); // 保留控制台日志
    }

    private static void logError(String message) {
        if (logConsumer != null) {
            logConsumer.accept("[错误] " + message,true);
        }
        System.err.println(message); // 保留控制台错误日志
    }


    private static void printConnectionPoolStats() {
        HikariDataSource cloudDataSource = dataSourceCloud;
        // 获取云数据源连接池监控信息
        HikariPoolMXBean cloudPoolMXBean = cloudDataSource.getHikariPoolMXBean();
        int cloudActiveConnections = cloudPoolMXBean.getActiveConnections();
        int cloudIdleConnections = cloudPoolMXBean.getIdleConnections();
        int cloudTotalConnections = cloudPoolMXBean.getTotalConnections();
        System.out.println("Cloud DataSource - Active: " + cloudActiveConnections + ", Idle: " + cloudIdleConnections + ", Total: " + cloudTotalConnections);
    }

    private static Connection getDatabaseConnection() throws SQLException {
        return dataSource.getConnection();
    }

    // 确保正确关闭资源
    public static void shutdown() throws PulsarClientException {
        if (consumer != null && consumer.isConnected()) {
            consumer.close();
        }
        if (pulsarClient != null && !pulsarClient.isClosed()) {
            pulsarClient.close();
        }
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
        }
        if (dataSourceCloud != null && !dataSourceCloud.isClosed()) {
            dataSourceCloud.close();
        }
    }

    public static void stopApp() {
        if (taskThread != null) {
            taskThread.interrupt();
        }
        System.out.println("应用已关闭。");
    }

    static class ControlLimitsCache {
        private static final Map<String, ControlLimits> cache = new ConcurrentHashMap<>();
        private static final int MAX_CACHE_SIZE = 1000; // 限制缓存大小以节省内存

        public static ControlLimits getControlLimits(Connection remoteConnection, String fPrcs, String fTest,
                                                     String fPart) {
            // 构建一个唯一的键来标识每个不同的参数组合
            String key = fPrcs + "_" + fTest + "_" + fPart;

            // 首先尝试从缓存中获取
            if (cache.containsKey(key)) {
                return cache.get(key);
            }

            // 如果缓存中没有，则从数据库查询并更新缓存
            if (fPrcs != null && fTest != null && fPart != null) {
                String sql = "SELECT F_MEAN, F_SP FROM SPC.CTRL_LIM WHERE F_PRCS = ? AND F_TEST = ? AND F_PART = ? ORDER BY F_CTRL DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY";
                try (PreparedStatement preparedStatement = remoteConnection.prepareStatement(sql)) {
                    preparedStatement.setString(1, fPrcs);
                    preparedStatement.setString(2, fTest);
                    preparedStatement.setString(3, fPart);
                    try (ResultSet resultSet = preparedStatement.executeQuery()) {
                        if (resultSet.next()) {
                            double fMean = resultSet.getDouble("F_MEAN");
                            double fSp = resultSet.getDouble("F_SP");
                            ControlLimits limits = new ControlLimits(fMean, fSp);

                            // 检查缓存大小，如果超过限制则清理一半
                            if (cache.size() >= MAX_CACHE_SIZE) {
                                clearHalfCache();
                            }
                            cache.put(key, limits); // 更新缓存
                            return limits;
                        }
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            return null;
        }

        // 清理一半缓存以节省内存
        private static void clearHalfCache() {
            int targetSize = MAX_CACHE_SIZE / 2;
            cache.entrySet().removeIf(entry -> cache.size() > targetSize);
        }

        // 提供清理缓存的方法
        public static void clearCache() {
            cache.clear();
        }
    }

    private static class ControlLimits {
        double fMean;
        double fSp;

        public ControlLimits(double fMean, double fSp) {
            this.fMean = fMean;
            this.fSp = fSp;
        }
    }

    private static class CalculatedValues {
        double upperControlLimit;
        double lowerControlLimit;
        double lcl1;
        double ucl1;

        public CalculatedValues(double upperControlLimit, double lowerControlLimit, double lcl1, double ucl1) {
            this.upperControlLimit = upperControlLimit;
            this.lowerControlLimit = lowerControlLimit;
            this.lcl1 = lcl1;
            this.ucl1 = ucl1;
        }
    }

    private static void insertDataIntoChemicalYsTable(Connection connection, Chemical entity) {
        // 创建 SQL 插入语句，根据表列名进行调整
        String sql = "INSERT INTO dbo.chemical_ys (id, organization_id, attribute_id, examine_date, shift, staff, " +
                "process_set_name, process_name, product_set_name, product_name, test_set_name, " +
                "test_name, sample_size, layer_number, upper_limit, median_specification, down_limit, examine1, " +
                "examine2, created_by, last_updated_by, creation_date, last_update_date, status, frequency," +
                " " +
                "frequency_unit, slot_body_name, project_team_code, project_team_name, test_code, " +
                "adjustment_upper_limit, " +
                "adjustment_mid, adjustment_lower_limit, project_unit,insertion_time,warning_upper_limit," +
                "warning_mid,warning_lower_limit,examine1_zs) VALUES (?, ?, ?, ?, ?, ?," +
                " ?, ?, ?, ?," +
                " ?, ?, ?, ?, " +
                "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?)";
        try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            preparedStatement.setString(1, entity.getId());
            setNullableLong(preparedStatement, 2, entity.getOrganizationId());
            setNullableLong(preparedStatement, 3, entity.getAttributeId());
            preparedStatement.setTimestamp(4, entity.getExamineDate());
            preparedStatement.setString(5, entity.getShift());
            preparedStatement.setString(6, entity.getStaff());
            preparedStatement.setString(7, entity.getProcessSetName());
            preparedStatement.setString(8, entity.getProcessName());
            preparedStatement.setString(9, entity.getProductSetName());
            preparedStatement.setString(10, entity.getProductName());
            preparedStatement.setString(11, entity.getTestSetName());
            preparedStatement.setString(12, entity.getTestName());
            preparedStatement.setString(13, entity.getSampleSize());
            preparedStatement.setString(14, entity.getLayerNumber());
            preparedStatement.setString(15, entity.getUpperLimit());
            preparedStatement.setString(16, entity.getMedianSpecification());
            preparedStatement.setString(17, entity.getDownLimit());
            preparedStatement.setString(18, entity.getExamine1YS());
            preparedStatement.setString(19, entity.getExamine2());
            preparedStatement.setString(20, entity.getCreatedBy());
            preparedStatement.setString(21, entity.getLastUpdatedBy());
            preparedStatement.setTimestamp(22, entity.getCreationDate());
            preparedStatement.setTimestamp(23, entity.getLastUpdateDate());
            setNullableLong(preparedStatement, 24, entity.getStatus());
            preparedStatement.setString(25, entity.getFrequency());
            preparedStatement.setString(26, entity.getFrequencyUnit());
            preparedStatement.setString(27, entity.getSlotBodyName());
            preparedStatement.setString(28, entity.getProjectTeamCode());
            preparedStatement.setString(29, entity.getProjectTeamName());
            preparedStatement.setString(30, entity.getTestCode());
            preparedStatement.setString(31, entity.getAdjustmentUpperLimit());
            preparedStatement.setString(32, entity.getAdjustmentMid());
            preparedStatement.setString(33, entity.getAdjustmentLowerLimit());
            preparedStatement.setString(34, entity.getProjectUnit());
            preparedStatement.setTimestamp(35, new Timestamp(System.currentTimeMillis()));
            preparedStatement.setString(36, entity.getWarningUpperLimit());
            preparedStatement.setString(37, entity.getWarningMid());
            preparedStatement.setString(38, entity.getWarningLowerLimit());
            preparedStatement.setString(39, entity.getExamine1());
            int rowsInserted = preparedStatement.executeUpdate();
            if (rowsInserted > 0) {
                log("应审数据插入成功：" + entity.getId()+" "+entity.getTestName());
            }
        } catch (SQLException e) {
            log("应审插入数据时出错：" + e.getMessage());
            handleSQLException(e);
        }
    }

    public static Chemical resultToChemical(String result,String examine1) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        String[] parts = result.split(",", -1); // 使用-1保留空值
        Chemical chemical = new Chemical();
        if (result!=null) {
            try {
                chemical.setId(parts[0]);
                chemical.setOrganizationId(Long.valueOf(parts[1]));
                chemical.setAttributeId(Long.valueOf(parts[2]));
                chemical.setExamineDate(new Timestamp(dateFormat.parse(parts[3]).getTime()));
                chemical.setShift(parts[4]);
                chemical.setStaff(parts[5]);
                chemical.setProcessSetName(parts[6]);
                chemical.setProcessName(parts[7]);
                chemical.setProductSetName(parts[8]);
                chemical.setProductName(parts[9]);
                chemical.setTestSetName(parts[10]);
                chemical.setTestName(parts[11]);
                chemical.setSampleSize(parts[12]);
                chemical.setLayerNumber(parts[13]);
                chemical.setUpperLimit(parts[14]);
                chemical.setMedianSpecification(parts[15]);
                chemical.setDownLimit(parts[16]);
                chemical.setExamine1YS(parts[17]);
                chemical.setExamine2(parts[18]);
                chemical.setCreatedBy(parts[19]);
                chemical.setLastUpdatedBy(parts[20]);
                chemical.setCreationDate(new Timestamp(dateFormat.parse(parts[21]).getTime()));
                chemical.setLastUpdateDate(new Timestamp(dateFormat.parse(parts[22]).getTime()));
                chemical.setStatus(Long.parseLong(parts[23]));
                chemical.setFrequency(parts[24]);
                chemical.setFrequencyUnit(parts[25]);
                chemical.setSlotBodyName(parts[26]);
                chemical.setProjectTeamCode(parts[27]);
                chemical.setProjectTeamName(parts[28]);
                chemical.setTestCode(parts[29]);
                chemical.setAdjustmentUpperLimit(parts[30]);
                chemical.setAdjustmentMid(parts[31]);
                chemical.setAdjustmentLowerLimit(parts[32]);
                chemical.setProjectUnit(parts[33]);
                chemical.setWarningUpperLimit(parts[34]);
                chemical.setWarningMid(parts[35]);
                chemical.setWarningLowerLimit(parts[36]);
                chemical.setExamine1(examine1);
            } catch (Exception e) {
                e.printStackTrace();
                // Handle parsing error
            }
            return chemical;
        }
        return null;
    }

}
