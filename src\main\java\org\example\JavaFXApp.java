package org.example;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.text.Font;
import javafx.stage.Stage;
import javafx.scene.Scene;
import org.apache.pulsar.client.api.PulsarClientException;

import java.util.function.BiConsumer;

public class JavaFXApp extends Application {

    private static Label statusLabel; // 将状态标签设为静态变量
    @Override
    public void start(Stage primaryStage) {
        primaryStage.setTitle("SZ301药水应审软件");

        statusLabel = new Label("当前状态：等待开始"); // 初始化状态标签
        // 创建按钮和设置首选大小
        Button startButton = new Button("开始任务");
        Button pauseButton = new Button("暂停任务");
        Button stopButton = new Button("结束任务");




        // 设置按钮容器的布局
        HBox buttonsBox = new HBox(20, startButton, pauseButton, stopButton);
        buttonsBox.setAlignment(Pos.CENTER);

        // 创建状态显示标签
        statusLabel.setFont(new Font(16)); // 设置字体大小

        // 创建日志显示区域
        TextArea logTextArea = new TextArea();
        logTextArea.setEditable(false);
        logTextArea.setPrefHeight(200);
        logTextArea.setWrapText(true);

        // 将所有容器添加到主容器
        VBox mainBox = new VBox(30, buttonsBox, statusLabel, logTextArea);
        mainBox.setAlignment(Pos.CENTER);
        mainBox.setPadding(new Insets(30, 30, 30, 30));

        // 设置场景和显示舞台
        Scene scene = new Scene(mainBox, 800, 600);
        scene.getStylesheets().add(getClass().getResource("/style.css").toExternalForm());
        primaryStage.setScene(scene);
        primaryStage.getIcons().add(new Image(getClass().getResourceAsStream("/icons/app_icon.png")));
        primaryStage.show();

        // 优化：定义一个方法来更新日志区域，添加缓冲机制
        StringBuilder logBuffer = new StringBuilder();
        final int[] logCount = {0};

        BiConsumer<String, Boolean> updateLog = (message, isError) -> {
            // 缓冲日志消息，减少UI更新频率
            synchronized (logBuffer) {
                if (isError) {
                    logBuffer.append("[错误] ").append(message).append("\n");
                } else {
                    logBuffer.append("[信息] ").append(message).append("\n");
                }
                logCount[0]++;

                // 每10条消息或遇到错误时更新UI
                if (logCount[0] >= 10 || isError) {
                    String bufferedText = logBuffer.toString();
                    logBuffer.setLength(0);
                    logCount[0] = 0;

                    Platform.runLater(() -> {
                        logTextArea.appendText(bufferedText);
                        // 限制日志区域的文本长度，避免内存占用过大
                        if (logTextArea.getLength() > 50000) {
                            logTextArea.deleteText(0, 25000);
                        }
                    });
                }
            }
        };

        // 优化：初始化按钮状态
        pauseButton.setDisable(true);
        stopButton.setDisable(true);

        // 优化：开始任务按钮的事件处理，添加状态检查
        startButton.setOnAction(event -> {
            if (!Main.isRunning()) {
                Main.setLogConsumer(updateLog);
                try {
                    Main.startTask();
                    statusLabel.setText("当前状态：任务运行中");
                    updateLog.accept("任务已启动。", false);
                    // 优化：禁用开始按钮，启用其他按钮
                    startButton.setDisable(true);
                    pauseButton.setDisable(false);
                    stopButton.setDisable(false);
                } catch (PulsarClientException e) {
                    updateLog.accept("启动任务时发生错误：" + e.getMessage(), true);
                    Alert alert = new Alert(Alert.AlertType.ERROR, "启动任务时发生错误。", ButtonType.OK);
                    alert.showAndWait();
                }
            }
        });

        // 优化：暂停任务按钮的事件处理，添加状态检查
        pauseButton.setOnAction(event -> {
            if (Main.isRunning()) {
                if (Main.isPaused) {
                    Main.resumeTask();
                    statusLabel.setText("当前状态：任务运行中");
                    updateLog.accept("任务已恢复。", false);
                    pauseButton.setText("暂停任务");
                } else {
                    Main.pauseTask();
                    statusLabel.setText("当前状态：任务暂停");
                    updateLog.accept("任务已暂停。", false);
                    pauseButton.setText("恢复任务");
                }
            }
        });

        // 优化：结束任务按钮的事件处理，添加状态检查
        stopButton.setOnAction(event -> {
            if (Main.isRunning()) {
                try {
                    Main.stopTask();
                    statusLabel.setText("当前状态：任务已停止");
                    updateLog.accept("任务已停止。", false);
                    // 优化：重置按钮状态
                    startButton.setDisable(false);
                    pauseButton.setDisable(true);
                    pauseButton.setText("暂停任务");
                    stopButton.setDisable(true);
                } catch (PulsarClientException e) {
                    e.printStackTrace();
                    updateLog.accept("停止任务时发生错误：" + e.getMessage(), true);
                    Alert alert = new Alert(Alert.AlertType.ERROR, "停止任务时发生错误。", ButtonType.OK);
                    alert.showAndWait();
                }
            }
        });




    }

    @Override
    public void stop() throws PulsarClientException {
        // 设置应用程序停止标志
        Main.stopApp();

        // 等待后台线程完成
        if (Main.taskThread != null) {
            try {
                Main.taskThread.join(); // 等待线程完成执行
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 重新设置中断状态
                e.printStackTrace();
            }
        }
    }




    public static void main(String[] args) {
        launch(args);
    }
}
