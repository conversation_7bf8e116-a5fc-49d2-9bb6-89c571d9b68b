package org.example;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.text.Font;
import javafx.stage.Stage;
import javafx.scene.Scene;
import javafx.stage.DirectoryChooser;
import org.apache.pulsar.client.api.PulsarClientException;

import java.io.File;
import java.time.LocalDate;
import java.util.function.BiConsumer;

import static org.example.Main.filePath;
import static org.example.Main.isDateFilterActive;

public class JavaFXApp extends Application {

    private static Label statusLabel; // 将状态标签设为静态变量
    @Override
    public void start(Stage primaryStage) {
        primaryStage.setTitle("SZ301药水应审软件");

        statusLabel = new Label("当前状态：等待开始"); // 初始化状态标签
        // 创建按钮和设置首选大小
        Button startButton = new Button("开始任务");
        Button pauseButton = new Button("暂停任务");
        Button stopButton = new Button("结束任务");
        Button choosePathButton = new Button("选择路径");
        Button dateFilterButton = new Button("筛选日期");


        // 设置日期选择器的首选大小和格式
        DatePicker startDatePicker = new DatePicker();
        DatePicker endDatePicker = new DatePicker();
        startDatePicker.setPrefSize(120, 30);
        endDatePicker.setPrefSize(120, 30);
        startDatePicker.setValue(LocalDate.now());
        endDatePicker.setValue(LocalDate.now());

        // 设置文本域和标签的首选大小和样式
        TextField pathTextField = new TextField();


        Label pathLabel = new Label("保存路径：");
        Label startDateLabel = new Label("开始日期:");
        Label endDateLabel = new Label("结束日期:");

        // 设置按钮容器的布局
        HBox buttonsBox = new HBox(20, startButton, pauseButton, stopButton, choosePathButton);
        buttonsBox.setAlignment(Pos.CENTER);

        // 设置日期选择器容器的布局
        HBox datePickersBox = new HBox(20, startDateLabel, startDatePicker, endDateLabel, endDatePicker,
                dateFilterButton);
        datePickersBox.setAlignment(Pos.CENTER);

        // 设置路径文本域容器的布局
        HBox pathBox = new HBox(20, pathLabel, pathTextField);
        pathBox.setAlignment(Pos.CENTER);

        // 创建状态显示标签
        statusLabel.setFont(new Font(16)); // 设置字体大小

        // 创建日志显示区域
        TextArea logTextArea = new TextArea();
        logTextArea.setEditable(false);
        logTextArea.setPrefHeight(200);
        logTextArea.setWrapText(true);

        // 将所有容器添加到主容器
        VBox mainBox = new VBox(30, buttonsBox, pathBox, datePickersBox, statusLabel,logTextArea);
        mainBox.setAlignment(Pos.CENTER);
        mainBox.setPadding(new Insets(30, 30, 30, 30));

        // 设置场景和显示舞台
        Scene scene = new Scene(mainBox, 800, 600);
        scene.getStylesheets().add(getClass().getResource("/style.css").toExternalForm());
        primaryStage.setScene(scene);
        primaryStage.getIcons().add(new Image(getClass().getResourceAsStream("/icons/app_icon.png")));
        primaryStage.show();

        // 定义一个方法来更新日志区域
        BiConsumer<String, Boolean> updateLog = (message, isError) -> {
            Platform.runLater(() -> {
                if (isError) {
                    logTextArea.appendText("[错误] " + message + "\n");
                } else {
                    logTextArea.appendText("[信息] " + message + "\n");
                }
            });
        };

        // 开始任务按钮的事件处理
        startButton.setOnAction(event -> {
            Main.setLogConsumer(updateLog);
            try {
                Main.startTask();
            } catch (PulsarClientException e) {
                throw new RuntimeException(e);
            }
            statusLabel.setText("当前状态：任务运行中");
            updateLog.accept("任务已启动。", false);
        });

        // 暂停任务按钮的事件处理
        pauseButton.setOnAction(event -> {
            if (Main.isPaused) {
                Main.resumeTask();
                statusLabel.setText("当前状态：任务运行中");
                updateLog.accept("任务已恢复。", false);
            } else {
                Main.pauseTask();
                statusLabel.setText("当前状态：任务暂停");
                updateLog.accept("任务已暂停。", false);
            }
        });

        // 结束任务按钮的事件处理
        stopButton.setOnAction(event -> {
            try {
                Main.stopTask();
                statusLabel.setText("当前状态：任务已停止");
                updateLog.accept("任务已停止。", false);
            } catch (PulsarClientException e) {
                e.printStackTrace();
                updateLog.accept("停止任务时发生错误：" + e.getMessage(), true);
                Alert alert = new Alert(Alert.AlertType.ERROR, "停止任务时发生错误。", ButtonType.OK);
                alert.showAndWait();
            }
        });


        // 路径选择按钮的事件处理
        choosePathButton.setOnAction(event -> {
            DirectoryChooser directoryChooser = new DirectoryChooser();
            File selectedDirectory = directoryChooser.showDialog(primaryStage);

            if (selectedDirectory != null) {
                pathTextField.setText(selectedDirectory.getAbsolutePath());
                Main.filePath = selectedDirectory.getAbsolutePath();
                updateLog.accept("保存路径已选择：" + Main.filePath, false);
            }
        });

        // 日期筛选按钮的事件处理
        dateFilterButton.setOnAction(event -> {
            if (!isPathSelected()) {
                // 如果路径没有选择，显示警告信息
                Alert alert = new Alert(Alert.AlertType.WARNING, "请先选择一个保存路径。", ButtonType.OK);
                alert.showAndWait();
                statusLabel.setText("当前状态：等待路径选择");
                updateLog.accept("未选择保存路径，无法筛选日期。", false);
            } else {
                isDateFilterActive = true;
                LocalDate startDate = startDatePicker.getValue();
                LocalDate endDate = endDatePicker.getValue();
                Main.setProcessingDates(startDate, endDate);
                System.out.println("开始日期：" + startDate + "，结束日期：" + endDate);
                try {
                    Main.startTask();
                } catch (PulsarClientException e) {
                    throw new RuntimeException(e);
                }
                statusLabel.setText("当前状态：进行日期筛选的任务运行中");
                updateLog.accept("任务启动，筛选日期从 " + startDate + " 到 " + endDate, false);
            }
        });

    }

    @Override
    public void stop() throws PulsarClientException {
        // 设置应用程序停止标志
        Main.stopApp();

        // 等待后台线程完成
        if (Main.taskThread != null) {
            try {
                Main.taskThread.join(); // 等待线程完成执行
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 重新设置中断状态
                e.printStackTrace();
            }
        }
    }

    private boolean isPathSelected() {
        return filePath != null && !filePath.trim().isEmpty();
    }


    public static void main(String[] args) {
        launch(args);
    }
}
